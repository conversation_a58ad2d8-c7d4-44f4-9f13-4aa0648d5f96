using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 零件接口，定义装配零件的基本属性
/// </summary>
public interface IAssemblyPart
{
    Transform PartTransform { get; }
    string PartName { get; }

    // 参考点属性（适用于所有零件）
    Transform[] ReferencePoints { get; } // 获取所有参考点
    Transform GetReferencePoint(int index); // 获取指定索引的参考点
    int ReferencePointCount { get; } // 获取参考点数量
    bool HasReferencePoints { get; } // 是否有参考点

    // 向后兼容的属性和方法
    Transform MountPoint { get; }  // 兼容旧版本的主挂载点（映射到第一个参考点）
    Transform GetMountPoint(int index); // 获取指定索引的挂载点（映射到参考点）
}

/// <summary>
/// 零件类型枚举
/// </summary>
public enum PartType
{
    Generic,    // 通用零件
    LongU,      // 长U型架
    ShortU,     // 短U型架
    Screw,      // 螺丝
    Nut         // 螺母
}

/// <summary>
/// 基础零件组件实现
/// 将此组件添加到需要参与装配动画的物体上
/// </summary>
public class AssemblyPart : MonoBehaviour, IAssemblyPart
{
    [Header("基本设置")]
    [SerializeField] private PartType partType = PartType.Generic;
    [Tooltip("零件类型，影响装配行为")]

    [Header("参考点")]
    [SerializeField] private Transform[] referencePoints;
    [Tooltip("零件的参考点，用于对齐和装配。每个零件至少需要一个参考点。")]

    // 公共属性
    public Transform PartTransform => transform;
    public string PartName => gameObject.name;
    public PartType Type => partType;

    // 参考点相关属性
    public Transform[] ReferencePoints => referencePoints;
    public bool HasReferencePoints => referencePoints != null && referencePoints.Length > 0;
    public int ReferencePointCount => referencePoints != null ? referencePoints.Length : 0;

    // 向后兼容的属性
    public Transform MountPoint => GetReferencePoint(0); // 映射到第一个参考点

    /// <summary>
    /// 获取指定索引的参考点
    /// </summary>
    /// <param name="index">参考点索引</param>
    /// <returns>参考点Transform，如果索引无效则返回null</returns>
    public Transform GetReferencePoint(int index)
    {
        // 如果只有一个参考点，总是返回它，忽略索引
        if (referencePoints != null && referencePoints.Length == 1)
        {
            return referencePoints[0];
        }

        // 正常的索引检查
        if (referencePoints != null && index >= 0 && index < referencePoints.Length)
        {
            return referencePoints[index];
        }

        Debug.LogWarning($"零件 '{PartName}' 没有索引为 {index} 的参考点！");
        return null;
    }

    /// <summary>
    /// 通过名称获取参考点
    /// </summary>
    /// <param name="name">参考点名称</param>
    /// <returns>参考点Transform，如果找不到则返回null</returns>
    public Transform GetReferencePointByName(string name)
    {
        if (referencePoints == null) return null;

        foreach (var point in referencePoints)
        {
            if (point != null && point.name == name)
            {
                return point;
            }
        }

        Debug.LogWarning($"零件 '{PartName}' 没有名为 '{name}' 的参考点！");
        return null;
    }

    /// <summary>
    /// 获取指定索引的挂载点（向后兼容方法）
    /// </summary>
    /// <param name="index">挂载点索引</param>
    /// <returns>映射到参考点的Transform</returns>
    public Transform GetMountPoint(int index)
    {
        // 直接映射到参考点
        return GetReferencePoint(index);
    }

    private void OnValidate()
    {
        // 验证参考点
        if (referencePoints == null || referencePoints.Length == 0)
        {
            Debug.LogWarning($"零件 '{gameObject.name}' 没有设置任何参考点！请创建子物体并设置为参考点。", this);
        }
        else
        {
            for (int i = 0; i < referencePoints.Length; i++)
            {
                if (referencePoints[i] == null)
                {
                    Debug.LogWarning($"零件 '{gameObject.name}' 的参考点 {i} 未设置！", this);
                }
            }
        }

        // 确保referencePoints数组被初始化
        referencePoints ??= new Transform[0];
    }

    /// <summary>
    /// 验证参考点数组，确保所有引用都有效
    /// </summary>
    public void ValidateReferencePoints()
    {
        if (referencePoints == null)
        {
            Debug.LogError($"零件 '{PartName}' 的参考点数组为null！");
            return;
        }

        // 移除空引用
        List<Transform> validPoints = new List<Transform>();
        for (int i = 0; i < referencePoints.Length; i++)
        {
            if (referencePoints[i] != null)
            {
                validPoints.Add(referencePoints[i]);
            }
            else
            {
                Debug.LogWarning($"零件 '{PartName}' 的参考点 {i} 为null，将被移除。");
            }
        }

        // 更新数组
        if (validPoints.Count != referencePoints.Length)
        {
            Debug.Log($"零件 '{PartName}' 的参考点数组已更新：从 {referencePoints.Length} 个点减少到 {validPoints.Count} 个有效点。");
            referencePoints = validPoints.ToArray();
        }

        // 打印参考点信息
        for (int i = 0; i < referencePoints.Length; i++)
        {
            Debug.Log($"零件 '{PartName}' 的参考点 {i}: {referencePoints[i].name}, 位置: {referencePoints[i].position}, 旋转: {referencePoints[i].rotation.eulerAngles}");
        }
    }



    private void OnDrawGizmosSelected()
    {
        // 绘制参考点
        if (referencePoints != null)
        {
            for (int i = 0; i < referencePoints.Length; i++)
            {
                if (referencePoints[i] != null)
                {
                    // 使用不同颜色区分不同的参考点
                    var color = new Color(
                        0.2f + i * 0.2f % 0.8f,
                        0.8f,
                        0.2f + (i + 2) * 0.2f % 0.8f,
                        0.8f
                    );
                    DrawReferencePointGizmo(referencePoints[i], color, i);
                }
            }
        }
    }

    /// <summary>
    /// 绘制参考点的Gizmo
    /// </summary>
    private void DrawReferencePointGizmo(Transform point, Color color, int index)
    {
        Gizmos.color = color;
        Gizmos.DrawSphere(point.position, 0.012f);
        Gizmos.DrawLine(transform.position, point.position);

        // 绘制坐标轴
        Gizmos.color = Color.red;
        Gizmos.DrawRay(point.position, point.right * 0.05f);
        Gizmos.color = Color.green;
        Gizmos.DrawRay(point.position, point.up * 0.05f);
        Gizmos.color = Color.blue;
        Gizmos.DrawRay(point.position, point.forward * 0.05f);

        // 绘制索引标记
        Gizmos.color = Color.white;
        Gizmos.DrawSphere(point.position + Vector3.up * 0.02f, 0.005f);

        // 绘制索引数字
        UnityEditor.Handles.color = Color.white;
        UnityEditor.Handles.Label(point.position + Vector3.up * 0.03f, index.ToString());
    }
}
