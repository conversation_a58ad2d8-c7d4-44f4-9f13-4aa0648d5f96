using UnityEngine;
using SimpleJSON;

public class ToolExecutor : MonoBehaviour
{
    // 工具管理类（需在Inspector中赋值）
    public AgentTools agentTools;

    // 模拟接收到的工具序列（实际中从API获取）
    [TextArea] public string toolSequence = @"{
        ""tools"": [
            {
                ""name"": ""ShowTextUI"",
                ""params"": {
                    ""text"": ""Check assembly""
                }
            },
            {
                ""name"": ""HighlightPart"",
                ""params"": {
                    ""targetPartName"": ""舵盘""
                }
            },
                        {
                ""name"": ""HighlightPart"",
                ""params"": {
                    ""targetPartName"": ""舵盘1""
                }
            },
            {
                ""name"": ""ConnectParts"",
                ""params"": {
                    ""part1Name"": ""舵盘"",
                    ""part2Name"": ""舵盘1""
                }
            }
        ]
    }";

    private JSONNode toolList;
    private int currentIndex = 0;

    void Start()
    {
        // 解析工具序列
        toolList = JSON.Parse(toolSequence)["tools"].AsArray;
        if (toolList == null)
        {
            Debug.LogError("Invalid tool sequence");
        }
    }

    void Update()
    {
                    Debug.Log(Input.GetKeyDown(KeyCode.Space));
        // 按空格键执行下一个工具
        if (Input.GetKeyDown(KeyCode.Space) && currentIndex < toolList.Count)
        {
            Debug.Log(1);
            ExecuteNextTool();
        }
    }

    private void ExecuteNextTool()
    {
        if (currentIndex >= toolList.Count)
        {
            Debug.Log("All tools executed");
            return;
        }

        JSONNode tool = toolList[currentIndex];
        string toolName = tool["name"];
        JSONNode paramsNode = tool["params"];

        switch (toolName)
        {
            case "ShowTextUI":
                agentTools.ShowTextUI(paramsNode["text"]);
                break;

            case "HighlightPart":
                agentTools.HighlightPart(paramsNode["targetPartName"]);
                break;

            case "ConnectParts":
                agentTools.ConnectParts(paramsNode["part1Name"], paramsNode["part2Name"]);
                break;

            case "AssembleDisassembleScrew":
                Vector3 point = new Vector3(
                    paramsNode["partPoint"][0].AsFloat,
                    paramsNode["partPoint"][1].AsFloat,
                    paramsNode["partPoint"][2].AsFloat
                );
                agentTools.AssembleDisassembleScrew(point, paramsNode["assemblyEdge"], paramsNode["isAssemble"].AsBool);
                break;

            case "HighlightSubgraphSequence":
                // 假设序列化为字符串数组，需手动解析
                string[] parts = paramsNode["partSequence"].Value.Split(',');
                agentTools.HighlightSubgraphSequence(parts);
                break;

            case "GuideCheckParts":
                string[] partList = paramsNode["partList"].Value.Split(',');
                agentTools.GuideCheckParts(partList);
                break;

            default:
                Debug.LogWarning("Unknown tool: " + toolName);
                break;
        }

        currentIndex++;
        Debug.Log($"Executed {toolName} at index {currentIndex - 1} at {System.DateTime.Now}");
    }
}