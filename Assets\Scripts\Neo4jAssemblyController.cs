using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using SimpleJSON;
using System.Linq;
using System;

/// <summary>
/// Neo4j装配控制器
///
/// 负责连接Neo4j数据库和装配动画系统，根据数据库中的装配关系执行动画
/// </summary>
public class Neo4jAssemblyController : MonoBehaviour
{
    [Header("数据源")]
    [SerializeField] private Neo4jConnector neo4jConnector; // Neo4j连接器引用
    [SerializeField] private MonoBehaviour externalDataProvider; // 外部数据提供者（PICO部署时使用）
    [SerializeField] private bool useExternalDataSource = false; // 默认使用Neo4j，保持向后兼容

    [Header("动画控制")]
    [SerializeField] private AssemblyAnimationManager animationManager; // 装配动画管理器引用
    [SerializeField] private float animationDuration = 1.0f; // 动画持续时间
    [SerializeField] private float delayBetweenSteps = 0.5f; // 步骤之间的延迟
    [SerializeField] private float animationSpeedRate = 1.0f; // 动画速率

    [Header("紧固件设置")]
    [SerializeField] private GameObject screwM2X6Prefab; // M2X6螺丝预制体（备用）
    [SerializeField] private GameObject screwBA5Prefab; // BA_5螺丝预制体（备用）
    [SerializeField] private GameObject nutPrefab; // 通用螺母预制体（备用）
    [SerializeField] private Transform fastenersContainer; // 紧固件容器，用于动态生成的紧固件

    [Header("场景中的紧固件")]
    [SerializeField] private Transform screwsContainer; // 场景中存放螺丝的容器
    [SerializeField] private Transform nutsContainer; // 场景中存放螺母的容器
    [SerializeField] private bool useSceneFasteners = true; // 是否使用场景中的紧固件

    [Header("用户界面")]
    [SerializeField] private Neo4jAssemblyUI uiController; // UI控制器引用
    [SerializeField] private bool autoStartAnimation = false; // 是否自动开始动画
    [SerializeField] private KeyCode nextStepKey = KeyCode.Space; // 下一步快捷键

    [Header("VR支持 - PICO部署时启用")]
    [SerializeField] private VRAssemblyManager vrAssemblyManager; // VR装配管理器（PICO部署时使用）
    [SerializeField] private MonoBehaviour vrInputManager; // VR输入管理器（PICO部署时使用）
    [SerializeField] private bool enableVRMode = false; // 默认禁用VR模式，保持向后兼容

    // 事件
    public event Action<string> OnPartSelected; // 零件选择事件
    public event Action<int> OnAssemblyStepsLoaded; // 装配步骤加载完成事件
    public event Action<int, int, string> OnStepExecuted; // 步骤执行事件
    public event Action OnAssemblyCompleted; // 装配完成事件

    // 零件初始状态结构体
    private struct PartInitialState
    {
        public Vector3 position;
        public Quaternion rotation;
        public string partName;
    }

    // 螺丝螺母初始状态结构体
    private struct FastenerInitialState
    {
        public Vector3 position;
        public Quaternion rotation;
        public bool isActive;
        public string fastenerName;
    }

    // 内部状态
    private string selectedPartName; // 当前选中的零件名称
    private Queue<AssemblyStep> assemblySteps = new Queue<AssemblyStep>(); // 装配步骤队列
    private List<AssemblyStep> allSteps = new List<AssemblyStep>(); // 所有装配步骤
    private int currentStepIndex = 0; // 当前步骤索引
    private bool isAnimating = false; // 是否正在执行动画
    private Dictionary<string, AssemblyPart> partNameToComponent = new Dictionary<string, AssemblyPart>(); // 零件名称到组件的映射
    private Dictionary<string, int> referencePointNameToIndex = new Dictionary<string, int>(); // 参考点名称到索引的映射
    private HashSet<string> visitedNodes = new HashSet<string>(); // 已访问的节点集合
    private HashSet<string> processedConnections = new HashSet<string>(); // 已处理的连接集合
    private AssemblyStep? lastExecutedStep = null; // 上一次执行的步骤，用于重播功能

    // 保存上一步骤中零件的初始状态
    private List<PartInitialState> lastStepInitialStates = new List<PartInitialState>();

    // 保存所有零件的初始状态
    private Dictionary<string, PartInitialState> allPartsInitialStates = new Dictionary<string, PartInitialState>();

    // 保存所有螺丝螺母的初始状态
    private Dictionary<string, FastenerInitialState> allFastenersInitialStates = new Dictionary<string, FastenerInitialState>();

    // 属性
    public float AnimationDuration => animationDuration;
    public bool HasRemainingSteps() => assemblySteps.Count > 0;

    /// <summary>
    /// 动画速率属性，控制所有动画的播放速度
    /// </summary>
    public float AnimationSpeedRate
    {
        get { return animationSpeedRate; }
        set
        {
            animationSpeedRate = Mathf.Clamp(value, 0.1f, 5.0f);
            // 同步更新动画管理器的速率
            if (animationManager != null)
            {
                animationManager.AnimationSpeedRate = animationSpeedRate;
            }
        }
    }

    // 装配步骤结构
    private struct AssemblyStep
    {
        public string movingPartName; // 移动零件名称
        public string targetPartName; // 目标零件名称
        public string movingPartRefPoint; // 移动零件参考点
        public string targetPartRefPoint; // 目标零件参考点
        public string connectionType; // 连接类型：DIRECT, SCREW, SNAP等
        public FastenerInfo fastener; // 紧固件信息
        public List<AdditionalMountPoint> additionalMountPoints; // 额外装配点列表

        // 紧固件信息结构
        public struct FastenerInfo
        {
            public string type; // 紧固件类型：SCREW_NUT, SCREW_ONLY, NONE等
            public string screwType; // 螺丝类型
            public string nutType; // 螺母类型
            public string screwPrefabName; // 螺丝预制体名称
            public string nutPrefabName; // 螺母预制体名称
        }

        // 额外装配点结构
        public struct AdditionalMountPoint
        {
            public string fromPoint; // 源零件参考点
            public string toPoint; // 目标零件参考点
            public string connectionType; // 连接类型
            public FastenerInfo fastener; // 紧固件信息
        }

        public AssemblyStep(string movingPart, string targetPart, string movingRef, string targetRef)
        {
            movingPartName = movingPart;
            targetPartName = targetPart;
            movingPartRefPoint = movingRef;
            targetPartRefPoint = targetRef;
            connectionType = "DIRECT"; // 默认为直接连接
            fastener = new FastenerInfo(); // 默认为空紧固件信息
            additionalMountPoints = new List<AdditionalMountPoint>(); // 初始化额外装配点列表
        }

        public AssemblyStep(string movingPart, string targetPart, string movingRef, string targetRef,
                           string connType, FastenerInfo fastenerInfo)
        {
            movingPartName = movingPart;
            targetPartName = targetPart;
            movingPartRefPoint = movingRef;
            targetPartRefPoint = targetRef;
            connectionType = connType;
            fastener = fastenerInfo;
            additionalMountPoints = new List<AdditionalMountPoint>(); // 初始化额外装配点列表
        }

        public override string ToString()
        {
            string baseInfo = $"{movingPartName}[{movingPartRefPoint}] -> {targetPartName}[{targetPartRefPoint}]";
            if (connectionType != "DIRECT" && !string.IsNullOrEmpty(fastener.type))
            {
                baseInfo = $"{baseInfo} [{connectionType}: {fastener.type}]";
            }

            if (additionalMountPoints != null && additionalMountPoints.Count > 0)
            {
                baseInfo += $" (额外装配点: {additionalMountPoints.Count}个)";
            }

            return baseInfo;
        }
    }

    void Start()
    {
        // 初始化数据源
        InitializeDataSource();

        // 验证必要组件
        if (!useExternalDataSource && neo4jConnector == null)
        {
            neo4jConnector = GetComponent<Neo4jConnector>();
            if (neo4jConnector == null)
            {
                Debug.LogError("Neo4jConnector组件未找到！请手动指定或添加到同一GameObject。");
                enabled = false;
                return;
            }
        }

        if (animationManager == null)
        {
            animationManager = FindObjectOfType<AssemblyAnimationManager>();
            if (animationManager == null)
            {
                Debug.LogError("AssemblyAnimationManager组件未找到！请手动指定。");
                enabled = false;
                return;
            }
        }

        if (uiController == null)
        {
            uiController = FindObjectOfType<Neo4jAssemblyUI>();
        }

        // 初始化零件映射
        InitializePartMapping();

        // 验证参考点映射
        ValidateReferencePointMappings();

        // 注册事件
        RegisterEvents();

        // 初始化VR系统
        InitializeVRSystem();

        // 初始化VR输入
        InitializeVRInput();
    }

    /// <summary>
    /// 初始化数据源
    /// </summary>
    private void InitializeDataSource()
    {
        if (useExternalDataSource)
        {
            // 查找外部数据提供者
            if (externalDataProvider == null)
            {
                externalDataProvider = GetComponent<ExternalSystemDataProvider>();
                if (externalDataProvider == null)
                {
                    externalDataProvider = FindObjectOfType<ExternalSystemDataProvider>();
                }
            }

            if (externalDataProvider == null)
            {
                Debug.LogWarning("[Neo4jAssemblyController] 外部数据提供者未找到，将回退到Neo4j模式");
                useExternalDataSource = false;
            }
            else
            {
                Debug.Log("[Neo4jAssemblyController] 使用外部数据源模式");
            }
        }
        else
        {
            Debug.Log("[Neo4jAssemblyController] 使用Neo4j数据源模式");
        }
    }

    /// <summary>
    /// 初始化VR输入系统
    /// </summary>
    private void InitializeVRInput()
    {
        if (!enableVRMode) return;

        // 查找VR输入管理器
        if (vrInputManager == null)
        {
            vrInputManager = GetComponent<VRInputManager>();
            if (vrInputManager == null)
            {
                vrInputManager = FindObjectOfType<VRInputManager>();
            }
        }

        if (vrInputManager != null)
        {
            // 尝试获取VRInputManager组件并注册事件
            VRInputManager inputManager = vrInputManager as VRInputManager;
            if (inputManager != null)
            {
                inputManager.OnPartSelected += HandleVRPartSelection;
                inputManager.OnNextStepRequested += ExecuteNextStep;
                inputManager.OnResetRequested += ResetAssembly;
                inputManager.OnReplayRequested += ReplayLastStep;
                Debug.Log("[Neo4jAssemblyController] VR输入系统初始化完成");
            }
            else
            {
                Debug.LogWarning("[Neo4jAssemblyController] VRInputManager类型转换失败");
            }
        }
        else
        {
            Debug.LogWarning("[Neo4jAssemblyController] VRInputManager未找到，VR输入功能将被禁用");
        }
    }

    void Update()
    {
        // 只在非VR模式下处理传统输入
        if (!enableVRMode)
        {
            // 处理用户输入
            if (Input.GetMouseButtonDown(0) && !isAnimating)
            {
                SelectPartWithRaycast();
            }

            // 执行下一步动画
            if (Input.GetKeyDown(nextStepKey) && !isAnimating && assemblySteps.Count > 0)
            {
                ExecuteNextStep();
            }
        }
    }

    /// <summary>
    /// 处理VR零件选择
    /// </summary>
    private void HandleVRPartSelection(AssemblyPart selectedPart)
    {
        if (selectedPart == null || isAnimating) return;

        selectedPartName = selectedPart.PartName;
        Debug.Log($"[VR] 选择零件: {selectedPartName}");

        // 清空之前的步骤
        ResetAssemblySteps();

        // 触发零件选择事件
        OnPartSelected?.Invoke(selectedPartName);

        // 根据数据源类型查询装配关系
        if (useExternalDataSource && externalDataProvider != null)
        {
            StartCoroutine(LoadAssemblyStepsFromExternalSource(selectedPartName));
        }
        else
        {
            StartCoroutine(QueryAssemblyRelationships(selectedPartName));
        }
    }

    /// <summary>
    /// 从外部数据源加载装配步骤
    /// </summary>
    private IEnumerator LoadAssemblyStepsFromExternalSource(string startPartName)
    {
        Debug.Log($"[Neo4jAssemblyController] 从外部数据源加载装配步骤: {startPartName}");

        // 尝试获取外部数据提供者
        IAssemblyDataProvider dataProvider = externalDataProvider as IAssemblyDataProvider;
        if (dataProvider != null)
        {
            yield return StartCoroutine(dataProvider.LoadAssemblySteps(startPartName, (assemblyStepsData) => {
                if (assemblyStepsData != null && assemblyStepsData.Count > 0)
                {
                    ProcessExternalAssemblySteps(assemblyStepsData);
                }
                else
                {
                    Debug.LogWarning($"[Neo4jAssemblyController] 外部数据源未返回装配步骤: {startPartName}");
                    OnAssemblyStepsLoaded?.Invoke(0);
                }
            }));
        }
        else
        {
            Debug.LogError($"[Neo4jAssemblyController] 外部数据提供者类型不匹配");
            OnAssemblyStepsLoaded?.Invoke(0);
        }
    }

    /// <summary>
    /// 公共方法：接收外部系统发送的装配数据
    /// </summary>
    public void ReceiveExternalAssemblyData(string partName, string jsonData)
    {
        Debug.Log($"[Neo4jAssemblyController] 接收外部装配数据: {partName}");

        try
        {
            // 解析JSON数据
            var assemblyStepsData = ParseExternalAssemblyData(jsonData);
            ProcessExternalAssemblySteps(assemblyStepsData);
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[Neo4jAssemblyController] 解析外部装配数据失败: {e.Message}");
            OnAssemblyStepsLoaded?.Invoke(0);
        }
    }

    /// <summary>
    /// 解析外部装配数据JSON（简化版本）
    /// </summary>
    private List<AssemblyStepData> ParseExternalAssemblyData(string jsonData)
    {
        var steps = new List<AssemblyStepData>();

        try
        {
            // 简化的JSON解析，避免复杂的类型问题
            Debug.Log($"[Neo4jAssemblyController] 解析JSON数据: {jsonData}");

            // 这里可以根据实际需要实现简单的JSON解析
            // 或者直接使用SimpleExternalDataReceiver来处理数据

            Debug.LogWarning("[Neo4jAssemblyController] 请使用SimpleExternalDataReceiver来发送装配数据");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[Neo4jAssemblyController] JSON解析错误: {e.Message}");
        }

        return steps;
    }

    /// <summary>
    /// 处理外部装配步骤数据（简化版本）
    /// </summary>
    private void ProcessExternalAssemblySteps(List<AssemblyStepData> assemblyStepsData)
    {
        // 由于类型问题，这里暂时简化处理
        // 实际使用时建议通过SimpleExternalDataReceiver来处理数据
        Debug.LogWarning("[Neo4jAssemblyController] ProcessExternalAssemblySteps方法需要完整的AssemblyStepData类型支持");
        Debug.LogWarning("[Neo4jAssemblyController] 请使用SimpleExternalDataReceiver.ReceiveAssemblyData方法来发送装配数据");

        OnAssemblyStepsLoaded?.Invoke(0);
    }

    /// <summary>
    /// 注册事件处理
    /// </summary>
    private void RegisterEvents()
    {
        if (uiController != null)
        {
            // 注册事件处理
            OnPartSelected += uiController.OnPartSelected;
            OnAssemblyStepsLoaded += uiController.OnAssemblyStepsLoaded;
            OnStepExecuted += uiController.OnStepExecuted;
            OnAssemblyCompleted += uiController.OnAssemblyCompleted;
        }
    }

    /// <summary>
    /// 取消注册事件处理
    /// </summary>
    private void UnregisterEvents()
    {
        if (uiController != null)
        {
            // 取消注册事件处理
            OnPartSelected -= uiController.OnPartSelected;
            OnAssemblyStepsLoaded -= uiController.OnAssemblyStepsLoaded;
            OnStepExecuted -= uiController.OnStepExecuted;
            OnAssemblyCompleted -= uiController.OnAssemblyCompleted;
        }
    }

    /// <summary>
    /// 初始化VR系统
    /// </summary>
    private void InitializeVRSystem()
    {
        if (!enableVRMode) return;

        // 自动查找VR装配管理器
        if (vrAssemblyManager == null)
        {
            vrAssemblyManager = FindObjectOfType<VRAssemblyManager>();
            if (vrAssemblyManager == null)
            {
                Debug.LogWarning("[Neo4jAssemblyController] VRAssemblyManager未找到，VR功能将被禁用");
                enableVRMode = false;
                return;
            }
        }

        Debug.Log("[Neo4jAssemblyController] VR系统初始化完成");
    }

    /// <summary>
    /// 等待VR系统准备完成（精确等待机制）
    /// </summary>
    private IEnumerator WaitForVRPreparation(AssemblyPart movingPart, AssemblyPart targetPart, string stepDescription)
    {
        Debug.Log($"[Neo4jAssemblyController] 启动VR准备流程: {stepDescription}");

        // 启动VR处理
        vrAssemblyManager.OnAssemblyStepStart(movingPart, targetPart, stepDescription);

        // 精确等待VR准备完成
        yield return StartCoroutine(vrAssemblyManager.WaitForVRPreparationComplete());

        Debug.Log("[Neo4jAssemblyController] VR准备流程完成，可以开始装配动画");
    }

    void OnDestroy()
    {
        // 取消注册事件
        UnregisterEvents();
    }

    /// <summary>
    /// 初始化零件名称到组件的映射
    /// </summary>
    private void InitializePartMapping()
    {
        // 查找场景中所有的AssemblyPart组件
        AssemblyPart[] allParts = FindObjectsOfType<AssemblyPart>();

        foreach (var part in allParts)
        {
            string partName = part.gameObject.name;
            partNameToComponent[partName] = part;
            Debug.Log($"映射零件: {partName} -> {part}");

            // 初始化参考点映射
            for (int i = 0; i < part.ReferencePointCount; i++)
            {
                Transform refPoint = part.GetReferencePoint(i);
                if (refPoint != null)
                {
                    string refName = $"{partName}_{refPoint.name}";
                    referencePointNameToIndex[$"{partName}_{refPoint.name}"] = i;
                    Debug.Log($"映射参考点: {refName} -> 索引 {i}");
                }
            }
        }

        Debug.Log($"零件映射初始化完成，共 {partNameToComponent.Count} 个零件，{referencePointNameToIndex.Count} 个参考点");

        // 保存所有零件的初始状态
        SaveAllPartsInitialState();

        // 保存所有螺丝螺母的初始状态
        SaveAllFastenersInitialState();
    }

    /// <summary>
    /// 保存所有零件的初始状态
    /// </summary>
    private void SaveAllPartsInitialState()
    {
        allPartsInitialStates.Clear();

        foreach (var pair in partNameToComponent)
        {
            string partName = pair.Key;
            AssemblyPart part = pair.Value;

            allPartsInitialStates[partName] = new PartInitialState
            {
                position = part.PartTransform.position,
                rotation = part.PartTransform.rotation,
                partName = partName
            };

            Debug.Log($"保存零件初始状态: {partName}, 位置: {part.PartTransform.position}, 旋转: {part.PartTransform.rotation.eulerAngles}");
        }
    }

    /// <summary>
    /// 保存所有螺丝螺母的初始状态
    /// </summary>
    private void SaveAllFastenersInitialState()
    {
        allFastenersInitialStates.Clear();

        // 保存螺丝容器中的所有螺丝状态
        if (screwsContainer != null)
        {
            foreach (Transform child in screwsContainer)
            {
                allFastenersInitialStates[child.name] = new FastenerInitialState
                {
                    position = child.position,
                    rotation = child.rotation,
                    isActive = child.gameObject.activeSelf,
                    fastenerName = child.name
                };

                Debug.Log($"保存螺丝初始状态: {child.name}, 位置: {child.position}, 旋转: {child.rotation.eulerAngles}, 激活状态: {child.gameObject.activeSelf}");
            }
        }

        // 保存螺母容器中的所有螺母状态
        if (nutsContainer != null)
        {
            foreach (Transform child in nutsContainer)
            {
                allFastenersInitialStates[child.name] = new FastenerInitialState
                {
                    position = child.position,
                    rotation = child.rotation,
                    isActive = child.gameObject.activeSelf,
                    fastenerName = child.name
                };

                Debug.Log($"保存螺母初始状态: {child.name}, 位置: {child.position}, 旋转: {child.rotation.eulerAngles}, 激活状态: {child.gameObject.activeSelf}");
            }
        }

        Debug.Log($"螺丝螺母初始状态保存完成，共 {allFastenersInitialStates.Count} 个紧固件");
    }

    /// <summary>
    /// 恢复所有零件的初始状态
    /// </summary>
    private void RestoreAllPartsInitialState()
    {
        foreach (var pair in allPartsInitialStates)
        {
            string partName = pair.Key;
            PartInitialState state = pair.Value;

            if (partNameToComponent.TryGetValue(partName, out AssemblyPart part))
            {
                part.PartTransform.position = state.position;
                part.PartTransform.rotation = state.rotation;

                Debug.Log($"恢复零件初始状态: {partName}, 位置: {state.position}, 旋转: {state.rotation.eulerAngles}");
            }
        }
    }

    /// <summary>
    /// 恢复所有螺丝螺母的初始状态
    /// </summary>
    private void RestoreAllFastenersInitialState()
    {
        foreach (var pair in allFastenersInitialStates)
        {
            string fastenerName = pair.Key;
            FastenerInitialState state = pair.Value;

            // 在螺丝容器中查找
            Transform fastenerTransform = null;
            if (screwsContainer != null)
            {
                fastenerTransform = screwsContainer.Find(fastenerName);
            }

            // 如果在螺丝容器中没找到，在螺母容器中查找
            if (fastenerTransform == null && nutsContainer != null)
            {
                fastenerTransform = nutsContainer.Find(fastenerName);
            }

            if (fastenerTransform != null)
            {
                // 恢复位置和旋转
                fastenerTransform.position = state.position;
                fastenerTransform.rotation = state.rotation;

                // 恢复激活状态
                fastenerTransform.gameObject.SetActive(state.isActive);

                Debug.Log($"恢复螺丝螺母初始状态: {fastenerName}, 位置: {state.position}, 旋转: {state.rotation.eulerAngles}, 激活状态: {state.isActive}");
            }
            else
            {
                Debug.LogWarning($"无法找到螺丝螺母: {fastenerName}");
            }
        }
    }

    /// <summary>
    /// 保存步骤中涉及的零件的初始状态
    /// </summary>
    private void SaveStepPartsInitialState(AssemblyStep step)
    {
        lastStepInitialStates.Clear();

        // 保存移动零件的状态
        if (partNameToComponent.TryGetValue(step.movingPartName, out AssemblyPart movingPart))
        {
            lastStepInitialStates.Add(new PartInitialState
            {
                position = movingPart.PartTransform.position,
                rotation = movingPart.PartTransform.rotation,
                partName = step.movingPartName
            });

            Debug.Log($"保存步骤零件状态: {step.movingPartName}, 位置: {movingPart.PartTransform.position}, 旋转: {movingPart.PartTransform.rotation.eulerAngles}");
        }

        // 保存目标零件的状态（可选，如果目标零件也会移动）
        if (partNameToComponent.TryGetValue(step.targetPartName, out AssemblyPart targetPart))
        {
            lastStepInitialStates.Add(new PartInitialState
            {
                position = targetPart.PartTransform.position,
                rotation = targetPart.PartTransform.rotation,
                partName = step.targetPartName
            });

            Debug.Log($"保存步骤零件状态: {step.targetPartName}, 位置: {targetPart.PartTransform.position}, 旋转: {targetPart.PartTransform.rotation.eulerAngles}");
        }
    }

    /// <summary>
    /// 恢复步骤中涉及的零件的初始状态
    /// </summary>
    private void RestoreStepPartsInitialState()
    {
        foreach (var state in lastStepInitialStates)
        {
            if (partNameToComponent.TryGetValue(state.partName, out AssemblyPart part))
            {
                part.PartTransform.position = state.position;
                part.PartTransform.rotation = state.rotation;

                Debug.Log($"恢复步骤零件状态: {state.partName}, 位置: {state.position}, 旋转: {state.rotation.eulerAngles}");
            }
        }
    }

    /// <summary>
    /// 使用射线检测选择零件
    /// </summary>
    private void SelectPartWithRaycast()
    {
        Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);
        if (Physics.Raycast(ray, out RaycastHit hit))
        {
            GameObject hitObject = hit.collider.gameObject;

            // 查找命中对象或其父对象上的AssemblyPart组件
            AssemblyPart part = hitObject.GetComponent<AssemblyPart>();
            if (part == null)
            {
                part = hitObject.GetComponentInParent<AssemblyPart>();
            }

            if (part != null)
            {
                selectedPartName = part.gameObject.name;
                Debug.Log($"选择零件: {selectedPartName}");

                // 清空之前的步骤
                ResetAssemblySteps();

                // 触发零件选择事件
                OnPartSelected?.Invoke(selectedPartName);

                // 查询Neo4j获取装配关系
                StartCoroutine(QueryAssemblyRelationships(selectedPartName));
            }
        }
    }

    /// <summary>
    /// 重置装配步骤
    /// </summary>
    private void ResetAssemblySteps()
    {
        assemblySteps.Clear();
        allSteps.Clear();
        currentStepIndex = 0;
        isAnimating = false;
        visitedNodes.Clear();
        processedConnections.Clear();
    }

    /// <summary>
    /// 查询Neo4j数据库获取装配关系
    /// </summary>
    /// <param name="startPartName">起始零件名称</param>
    private IEnumerator QueryAssemblyRelationships(string startPartName)
    {
        // 如果节点已经访问过，跳过
        if (visitedNodes.Contains(startPartName))
        {
            Debug.Log($"跳过已访问的零件: {startPartName}");
            yield break;
        }

        // 添加到已访问集合
        visitedNodes.Add(startPartName);
        Debug.Log($"开始查询零件 {startPartName} 的装配关系");

        // 构建Cypher查询，获取连接关系和参考点信息，以及新增的连接类型和紧固件信息
        // 修改查询，使用有向关系，只获取从startPartName出发的边
        // 注意：fastener信息现在是展平的属性，不再是嵌套对象
        string query = $"{{\"statements\": [{{\"statement\": \"MATCH (a {{name: '{startPartName}'}})-[r:CONNECTED]->(b) RETURN a.name, b.name, r.from_point, r.to_point, r.connection_type, r.fastener_type, r.screw_type, r.nut_type, r.additional_mount_points\"}}]}}";

        // 使用Neo4jConnector执行查询
        yield return StartCoroutine(neo4jConnector.ExecuteQuery(query, ProcessAssemblyRelationships));
    }

    /// <summary>
    /// 处理Neo4j查询结果，解析装配关系
    /// </summary>
    /// <param name="jsonResponse">JSON格式的响应数据</param>
    private void ProcessAssemblyRelationships(string jsonResponse)
    {
        if (string.IsNullOrEmpty(jsonResponse))
        {
            Debug.LogError("查询返回空结果！");
            OnAssemblyStepsLoaded?.Invoke(0);
            return;
        }

        JSONNode response = JSON.Parse(jsonResponse);
        if (response == null || response["results"].Count == 0)
        {
            Debug.LogError("JSON解析失败或无结果！");
            OnAssemblyStepsLoaded?.Invoke(0);
            return;
        }

        JSONNode dataArray = response["results"][0]["data"];
        if (dataArray == null || dataArray.Count == 0)
        {
            Debug.Log($"零件 {selectedPartName} 没有连接关系！");
            OnAssemblyStepsLoaded?.Invoke(0);
            return;
        }

        Debug.Log($"查询到 {dataArray.Count} 个连接关系");

        // 解析每个连接关系，创建装配步骤
        List<string> newPartsToQuery = new List<string>();

        foreach (JSONNode row in dataArray.AsArray)
        {
            string partA = row["row"][0];
            string partB = row["row"][1];
            string fromPoint = row["row"][2];
            string toPoint = row["row"][3];

            // 解析新增的连接类型和紧固件信息
            string connectionType = "DIRECT"; // 默认为直接连接
            AssemblyStep.FastenerInfo fastener = new AssemblyStep.FastenerInfo();

            // 检查是否有连接类型信息
            if (row["row"].Count > 4 && row["row"][4] != null && !row["row"][4].IsNull)
            {
                connectionType = row["row"][4];
                Debug.Log($"解析到连接类型: {connectionType}");
            }

            // 检查是否有紧固件类型信息（现在是展平的属性）
            if (row["row"].Count > 5 && row["row"][5] != null && !row["row"][5].IsNull)
            {
                fastener.type = row["row"][5];
                Debug.Log($"解析到紧固件类型: {fastener.type}");
            }

            // 检查是否有螺丝类型信息
            if (row["row"].Count > 6 && row["row"][6] != null && !row["row"][6].IsNull)
            {
                fastener.screwType = row["row"][6];
                // 根据螺丝类型设置预制体名称
                fastener.screwPrefabName = GetScrewPrefabName(fastener.screwType);
                Debug.Log($"解析到螺丝类型: {fastener.screwType}, 预制体: {fastener.screwPrefabName}");
            }

            // 检查是否有螺母类型信息
            if (row["row"].Count > 7 && row["row"][7] != null && !row["row"][7].IsNull)
            {
                fastener.nutType = row["row"][7];
                // 根据螺母类型设置预制体名称
                fastener.nutPrefabName = GetNutPrefabName(fastener.nutType);
                Debug.Log($"解析到螺母类型: {fastener.nutType}, 预制体: {fastener.nutPrefabName}");
            }

            // 创建唯一标识符，确保每对零件只处理一次
            string connectionKey = $"{partA}_{partB}_{fromPoint}_{toPoint}";
            string reverseKey = $"{partB}_{partA}_{toPoint}_{fromPoint}";

            if (!processedConnections.Contains(connectionKey) && !processedConnections.Contains(reverseKey))
            {
                // 添加到已处理集合
                processedConnections.Add(connectionKey);

                // 创建装配步骤，包含连接类型和紧固件信息
                AssemblyStep step = new AssemblyStep(partB, partA, toPoint, fromPoint, connectionType, fastener);

                // 检查是否有额外装配点信息
                if (row["row"].Count > 8 && row["row"][8] != null && !row["row"][8].IsNull)
                {
                    JSONNode additionalPointsNode = row["row"][8];
                    foreach (JSONNode pointInfo in additionalPointsNode.AsArray)
                    {
                        string[] parts = pointInfo.Value.Split(',');
                        if (parts.Length >= 5)  // 至少需要5个部分
                        {
                            AssemblyStep.AdditionalMountPoint mountPoint = new AssemblyStep.AdditionalMountPoint();
                            mountPoint.fromPoint = parts[0];
                            mountPoint.toPoint = parts[1];
                            mountPoint.connectionType = parts[2];

                            // 解析紧固件信息
                            AssemblyStep.FastenerInfo mountFastener = new AssemblyStep.FastenerInfo();
                            mountFastener.type = parts[3];

                            if (parts.Length > 4 && !string.IsNullOrEmpty(parts[4]))
                            {
                                mountFastener.screwType = parts[4];
                                mountFastener.screwPrefabName = GetScrewPrefabName(parts[4]);
                            }

                            if (parts.Length > 5 && !string.IsNullOrEmpty(parts[5]))
                            {
                                mountFastener.nutType = parts[5];
                                mountFastener.nutPrefabName = GetNutPrefabName(parts[5]);
                            }

                            mountPoint.fastener = mountFastener;
                            step.additionalMountPoints.Add(mountPoint);

                            Debug.Log($"解析到额外装配点: {mountPoint.fromPoint} -> {mountPoint.toPoint}, " +
                                     $"连接类型: {mountPoint.connectionType}, 紧固件类型: {mountPoint.fastener.type}, " +
                                     $"螺丝类型: {mountPoint.fastener.screwType}, 螺母类型: {mountPoint.fastener.nutType}");
                        }
                    }
                }

                assemblySteps.Enqueue(step);
                allSteps.Add(step);

                Debug.Log($"添加装配步骤: {step}");

                // 如果这个节点还没有被访问过，添加到待查询列表
                if (!visitedNodes.Contains(partB))
                {
                    newPartsToQuery.Add(partB);
                }
            }
            else
            {
                Debug.Log($"跳过重复的装配步骤: {partA}[{fromPoint}] <-> {partB}[{toPoint}]");
            }
        }

        // 触发步骤加载完成事件
        OnAssemblyStepsLoaded?.Invoke(allSteps.Count);

        // 为每个新发现的节点启动查询
        foreach (string partName in newPartsToQuery)
        {
            StartCoroutine(QueryAssemblyRelationships(partName));
        }

        // 如果设置了自动开始，则立即开始第一步
        if (autoStartAnimation && assemblySteps.Count > 0)
        {
            ExecuteNextStep();
        }
    }

    /// <summary>
    /// 执行下一个装配步骤
    /// </summary>
    public void ExecuteNextStep()
    {
        if (isAnimating || assemblySteps.Count == 0)
        {
            return;
        }

        StartCoroutine(ExecuteNextAssemblyStepCoroutine());
    }

    /// <summary>
    /// 重播上一个装配步骤
    /// </summary>
    public void ReplayLastStep()
    {
        if (isAnimating || lastExecutedStep == null)
        {
            return;
        }

        StartCoroutine(ReplayAssemblyStepCoroutine(lastExecutedStep.Value));
    }

    /// <summary>
    /// 执行下一个装配步骤的协程
    /// </summary>
    private IEnumerator ExecuteNextAssemblyStepCoroutine()
    {
        if (assemblySteps.Count == 0)
        {
            Debug.Log("没有更多装配步骤！");
            OnAssemblyCompleted?.Invoke();
            yield break;
        }

        isAnimating = true;
        AssemblyStep step = assemblySteps.Dequeue();
        lastExecutedStep = step; // 保存最后执行的步骤，用于重播功能

        // 保存步骤中涉及的零件的初始状态
        SaveStepPartsInitialState(step);

        currentStepIndex++;

        string stepDescription = $"执行步骤 {currentStepIndex}/{allSteps.Count}: {step}";
        Debug.Log(stepDescription);

        // 触发步骤执行事件
        OnStepExecuted?.Invoke(currentStepIndex, allSteps.Count, stepDescription);

        // 获取移动零件和目标零件
        if (!partNameToComponent.TryGetValue(step.movingPartName, out AssemblyPart movingPart) ||
            !partNameToComponent.TryGetValue(step.targetPartName, out AssemblyPart targetPart))
        {
            Debug.LogError($"找不到零件! 移动零件: {step.movingPartName}, 目标零件: {step.targetPartName}");
            isAnimating = false;
            yield break;
        }

        // 获取参考点索引
        int movingRefIndex = GetReferencePointIndex(step.movingPartName, step.movingPartRefPoint);
        int targetRefIndex = GetReferencePointIndex(step.targetPartName, step.targetPartRefPoint);

        if (movingRefIndex < 0 || targetRefIndex < 0)
        {
            Debug.LogError($"找不到参考点! 移动零件参考点: {step.movingPartRefPoint}, 目标零件参考点: {step.targetPartRefPoint}");
            isAnimating = false;
            yield break;
        }

        // 根据连接类型执行不同的动画
        Debug.Log($"执行装配: {movingPart.PartName}[{movingRefIndex}] -> {targetPart.PartName}[{targetRefIndex}], 连接类型: {step.connectionType}");

        // VR模式：装配步骤开始时的处理（等待VR准备完成）
        if (enableVRMode && vrAssemblyManager != null)
        {
            Debug.Log("[Neo4jAssemblyController] 等待VR系统准备完成...");

            // 启动VR处理并等待完成
            yield return StartCoroutine(WaitForVRPreparation(movingPart, targetPart, stepDescription));

            Debug.Log("[Neo4jAssemblyController] VR系统准备完成，开始装配动画");
        }

        // 先执行基本的对齐动画，使用同步方法确保动画完成
        yield return StartCoroutine(animationManager.AlignPartsSync(movingPart, targetPart, movingRefIndex, targetRefIndex, animationDuration));

        // 如果是螺丝连接，执行螺丝和螺母安装动画
        if (step.connectionType == "SCREW" && !string.IsNullOrEmpty(step.fastener.type))
        {
            Debug.Log($"执行紧固件安装: {step.fastener.type}");

            // 处理主要参考点的螺丝安装，索引为0
            yield return StartCoroutine(InstallFastener(step.fastener, targetPart, targetRefIndex, 0));

            // 处理额外装配点
            if (step.additionalMountPoints != null && step.additionalMountPoints.Count > 0)
            {
                int index = 1; // 从1开始，因为主要参考点已经使用了0
                foreach (var mountPoint in step.additionalMountPoints)
                {
                    if (mountPoint.connectionType == "SCREW" && !string.IsNullOrEmpty(mountPoint.fastener.type))
                    {
                        int fromIndex = GetReferencePointIndex(step.movingPartName, mountPoint.fromPoint);
                        int toIndex = GetReferencePointIndex(step.targetPartName, mountPoint.toPoint);

                        if (fromIndex >= 0 && toIndex >= 0)
                        {
                            Debug.Log($"安装额外装配点螺丝: {mountPoint.fromPoint} -> {mountPoint.toPoint}, " +
                                     $"螺丝类型: {mountPoint.fastener.screwType}, 索引: {index}");

                            yield return StartCoroutine(InstallFastener(mountPoint.fastener, targetPart, toIndex, index));
                            index++; // 递增索引

                            // 添加短暂延迟，使动画更有序列感
                            yield return new WaitForSeconds(0.5f);
                        }
                        else
                        {
                            Debug.LogWarning($"无法找到额外装配点参考点: {mountPoint.fromPoint} 或 {mountPoint.toPoint}");
                        }
                    }
                }
            }
        }

        // VR模式：装配步骤结束时的处理
        if (enableVRMode && vrAssemblyManager != null)
        {
            vrAssemblyManager.OnAssemblyStepEnd();
        }

        // 等待步骤之间的延迟
        yield return new WaitForSeconds(delayBetweenSteps);

        isAnimating = false;

        // 如果没有更多步骤，触发装配完成事件
        if (assemblySteps.Count == 0)
        {
            OnAssemblyCompleted?.Invoke();
        }
    }

    /// <summary>
    /// 重播装配步骤的协程
    /// </summary>
    /// <param name="step">要重播的步骤</param>
    private IEnumerator ReplayAssemblyStepCoroutine(AssemblyStep step)
    {
        isAnimating = true;

        string stepDescription = $"重播步骤 {currentStepIndex}/{allSteps.Count}: {step}";
        Debug.Log(stepDescription);

        // 触发步骤执行事件，但不增加步骤计数
        OnStepExecuted?.Invoke(currentStepIndex, allSteps.Count, $"[重播] {stepDescription}");

        // 恢复零件到步骤执行前的状态
        RestoreStepPartsInitialState();

        // 恢复螺丝螺母到初始状态
        RestoreAllFastenersInitialState();

        // 获取移动零件和目标零件
        if (!partNameToComponent.TryGetValue(step.movingPartName, out AssemblyPart movingPart) ||
            !partNameToComponent.TryGetValue(step.targetPartName, out AssemblyPart targetPart))
        {
            Debug.LogError($"找不到零件! 移动零件: {step.movingPartName}, 目标零件: {step.targetPartName}");
            isAnimating = false;
            yield break;
        }

        // 获取参考点索引
        int movingRefIndex = GetReferencePointIndex(step.movingPartName, step.movingPartRefPoint);
        int targetRefIndex = GetReferencePointIndex(step.targetPartName, step.targetPartRefPoint);

        if (movingRefIndex < 0 || targetRefIndex < 0)
        {
            Debug.LogError($"找不到参考点! 移动零件参考点: {step.movingPartRefPoint}, 目标零件参考点: {step.targetPartRefPoint}");
            isAnimating = false;
            yield break;
        }

        // 根据连接类型执行不同的动画
        Debug.Log($"重播装配: {movingPart.PartName}[{movingRefIndex}] -> {targetPart.PartName}[{targetRefIndex}], 连接类型: {step.connectionType}");

        // 先执行基本的对齐动画，使用同步方法确保动画完成
        yield return StartCoroutine(animationManager.AlignPartsSync(movingPart, targetPart, movingRefIndex, targetRefIndex, animationDuration));

        // 如果是螺丝连接，执行螺丝和螺母安装动画
        if (step.connectionType == "SCREW" && !string.IsNullOrEmpty(step.fastener.type))
        {
            Debug.Log($"重播紧固件安装: {step.fastener.type}");

            // 处理主要参考点的螺丝安装，索引为0
            yield return StartCoroutine(InstallFastener(step.fastener, targetPart, targetRefIndex, 0));

            // 处理额外装配点
            if (step.additionalMountPoints != null && step.additionalMountPoints.Count > 0)
            {
                int index = 1; // 从1开始，因为主要参考点已经使用了0
                foreach (var mountPoint in step.additionalMountPoints)
                {
                    if (mountPoint.connectionType == "SCREW" && !string.IsNullOrEmpty(mountPoint.fastener.type))
                    {
                        int fromIndex = GetReferencePointIndex(step.movingPartName, mountPoint.fromPoint);
                        int toIndex = GetReferencePointIndex(step.targetPartName, mountPoint.toPoint);

                        if (fromIndex >= 0 && toIndex >= 0)
                        {
                            Debug.Log($"重播额外装配点螺丝: {mountPoint.fromPoint} -> {mountPoint.toPoint}, " +
                                     $"螺丝类型: {mountPoint.fastener.screwType}, 索引: {index}");

                            yield return StartCoroutine(InstallFastener(mountPoint.fastener, targetPart, toIndex, index));
                            index++; // 递增索引

                            // 添加短暂延迟，使动画更有序列感
                            yield return new WaitForSeconds(0.5f);
                        }
                        else
                        {
                            Debug.LogWarning($"无法找到额外装配点参考点: {mountPoint.fromPoint} 或 {mountPoint.toPoint}");
                        }
                    }
                }
            }
        }

        // 等待步骤之间的延迟
        yield return new WaitForSeconds(delayBetweenSteps);

        isAnimating = false;
    }

    /// <summary>
    /// 重置装配状态
    /// </summary>
    public void ResetAssembly()
    {
        // 重置步骤
        ResetAssemblySteps();

        // 重置选中的零件
        selectedPartName = null;

        // 恢复所有零件到初始状态
        RestoreAllPartsInitialState();

        // 恢复所有螺丝螺母到初始状态
        RestoreAllFastenersInitialState();

        Debug.Log("装配状态已重置");
    }

    /// <summary>
    /// 安装紧固件（螺丝和螺母）
    /// </summary>
    /// <param name="fastener">紧固件信息</param>
    /// <param name="targetPart">目标零件</param>
    /// <param name="refIndex">参考点索引</param>
    /// <param name="fastenerIndex">紧固件索引，用于创建唯一的紧固件</param>
    /// <returns>协程</returns>
    private IEnumerator InstallFastener(AssemblyStep.FastenerInfo fastener, AssemblyPart targetPart, int refIndex, int fastenerIndex)
    {
        switch (fastener.type)
        {
            case "SCREW_NUT":
                // 创建螺丝和螺母，传入索引
                AssemblyPart screwPart = GetOrCreateFastener(fastener.screwPrefabName, fastenerIndex);
                AssemblyPart nutPart = GetOrCreateFastener(fastener.nutPrefabName, fastenerIndex);

                if (screwPart != null && nutPart != null)
                {
                    // 获取目标零件的参考点
                    Transform targetRef = targetPart.GetReferencePoint(refIndex);

                    // 获取参考点朝向方向
                    Vector3 holeDirection = targetRef.forward;

                    Debug.Log($"安装螺丝和螺母: 孔位位置 {targetRef.position}, 方向 {holeDirection}, " +
                             $"螺丝类型: {fastener.screwType}, 螺母类型: {fastener.nutType}, 索引: {fastenerIndex}");

                    // 执行螺丝安装动画
                    yield return StartCoroutine(animationManager.InstallScrew(screwPart, targetRef, holeDirection, animationDuration));

                    // 执行螺母安装动画
                    yield return StartCoroutine(animationManager.InstallNut(nutPart, targetRef, holeDirection, animationDuration));
                }
                else
                {
                    Debug.LogError("无法创建螺丝或螺母!");
                }
                break;

            case "SCREW_ONLY":
                // 只安装螺丝，传入索引
                AssemblyPart screwOnly = GetOrCreateFastener(fastener.screwPrefabName, fastenerIndex);

                if (screwOnly != null)
                {
                    Transform targetRef = targetPart.GetReferencePoint(refIndex);
                    Vector3 holeDirection = targetRef.forward;

                    Debug.Log($"只安装螺丝: 孔位位置 {targetRef.position}, 方向 {holeDirection}, " +
                             $"螺丝类型: {fastener.screwType}, 索引: {fastenerIndex}");

                    // 执行螺丝安装动画
                    yield return StartCoroutine(animationManager.InstallScrew(screwOnly, targetRef, holeDirection, animationDuration));
                }
                else
                {
                    Debug.LogError("无法创建螺丝!");
                }
                break;

            default:
                Debug.LogWarning($"未知的紧固件类型: {fastener.type}");
                break;
        }
    }

    /// <summary>
    /// 根据螺丝类型获取预制体名称
    /// </summary>
    private string GetScrewPrefabName(string screwType)
    {
        // 根据螺丝类型返回对应的预制体名称
        switch (screwType)
        {
            case "M2X6":
                return "ScrewM2X6";
            case "BA_5":
                return "ScrewBA_5";
            default:
                return "ScrewM2X6"; // 默认使用M2X6螺丝
        }
    }

    /// <summary>
    /// 根据螺母类型获取预制体名称
    /// </summary>
    private string GetNutPrefabName(string nutType)
    {
        // 根据螺母类型返回对应的预制体名称
        switch (nutType)
        {
            case "nut":
                return "Nut";
            default:
                return "Nut"; // 默认使用通用螺母
        }
    }

    /// <summary>
    /// 获取或创建紧固件
    /// </summary>
    /// <param name="prefabName">预制体名称</param>
    /// <param name="index">索引，用于创建唯一名称</param>
    /// <returns>紧固件的AssemblyPart组件</returns>
    private AssemblyPart GetOrCreateFastener(string prefabName, int index)
    {
        // 如果设置了使用场景中的紧固件，优先从场景中查找
        if (useSceneFasteners)
        {
            AssemblyPart sceneFastener = GetFastenerFromScene(prefabName, index);
            if (sceneFastener != null)
            {
                return sceneFastener;
            }

            // 如果从场景中找不到，回退到动态创建
            Debug.LogWarning($"场景中找不到紧固件: {prefabName}，将动态创建");
        }

        // 使用索引创建唯一的名称
        string uniqueName = $"{prefabName}_{index}";

        // 检查是否已经有同名的紧固件
        Transform existingFastener = fastenersContainer != null ? fastenersContainer.Find(uniqueName) : null;
        if (existingFastener != null)
        {
            AssemblyPart fastenerPart = existingFastener.GetComponent<AssemblyPart>();
            if (fastenerPart != null)
            {
                return fastenerPart;
            }
        }

        // 根据预制体名称选择合适的预制体
        GameObject prefab = null;
        if (prefabName.StartsWith("Screw"))
        {
            if (prefabName.Contains("M2X6") && screwM2X6Prefab != null)
            {
                prefab = screwM2X6Prefab;
            }
            else if (prefabName.Contains("BA_5") && screwBA5Prefab != null)
            {
                prefab = screwBA5Prefab;
            }
            // 可以添加更多类型的螺丝
        }
        else if (prefabName.StartsWith("Nut"))
        {
            if (nutPrefab != null)
            {
                prefab = nutPrefab;
            }
            // 可以添加更多类型的螺母
        }

        if (prefab == null)
        {
            Debug.LogError($"找不到紧固件预制体: {prefabName}");
            return null;
        }

        // 实例化预制体
        GameObject instance = Instantiate(prefab, Vector3.zero, Quaternion.identity);
        instance.name = uniqueName; // 使用唯一名称

        // 设置父对象
        if (fastenersContainer != null)
        {
            instance.transform.SetParent(fastenersContainer);
        }

        // 获取或添加AssemblyPart组件
        AssemblyPart part = instance.GetComponent<AssemblyPart>();
        if (part == null)
        {
            part = instance.AddComponent<AssemblyPart>();
            Debug.LogWarning($"紧固件预制体 {prefabName} 没有AssemblyPart组件，已自动添加");
        }

        return part;
    }

    /// <summary>
    /// 从场景中查找紧固件
    /// </summary>
    /// <param name="prefabName">预制体名称</param>
    /// <param name="index">索引，用于选择不同的紧固件</param>
    /// <returns>紧固件的AssemblyPart组件</returns>
    private AssemblyPart GetFastenerFromScene(string prefabName, int index)
    {
        // 确定是螺丝还是螺母
        Transform container = null;
        string fastenerType = "";

        if (prefabName.StartsWith("Screw"))
        {
            container = screwsContainer;
            fastenerType = prefabName.Replace("Screw", ""); // 例如："M2X6"
        }
        else if (prefabName.StartsWith("Nut"))
        {
            container = nutsContainer;
            fastenerType = prefabName.Replace("Nut", ""); // 例如：""（空字符串，表示通用螺母）
        }

        if (container == null)
        {
            Debug.LogWarning($"未设置{(prefabName.StartsWith("Screw") ? "螺丝" : "螺母")}容器!");
            return null;
        }

        // 查找所有符合类型的螺丝或螺母
        List<Transform> availableFasteners = new List<Transform>();
        foreach (Transform child in container)
        {
            // 检查名称是否包含类型
            if (child.name.Contains(fastenerType))
            {
                // 检查是否已经在使用中（激活状态）
                bool isInUse = child.gameObject.activeSelf;
                if (!isInUse)
                {
                    availableFasteners.Add(child);
                }
            }
        }

        // 如果没有找到可用的螺丝或螺母，返回null
        if (availableFasteners.Count == 0)
        {
            Debug.LogWarning($"场景中没有可用的{prefabName}!");
            return null;
        }

        // 获取一个可用的螺丝或螺母
        Transform fastener = availableFasteners[index % availableFasteners.Count];

        // 激活螺丝或螺母
        fastener.gameObject.SetActive(true);

        // 获取AssemblyPart组件
        AssemblyPart part = fastener.GetComponent<AssemblyPart>();
        if (part == null)
        {
            Debug.LogWarning($"螺丝或螺母 {fastener.name} 没有AssemblyPart组件，已自动添加");
            part = fastener.gameObject.AddComponent<AssemblyPart>();
        }

        Debug.Log($"从场景中找到紧固件: {fastener.name}");
        return part;
    }

    /// <summary>
    /// 清理所有紧固件
    /// </summary>
    private void CleanupFasteners()
    {
        // 清理动态创建的紧固件
        if (fastenersContainer != null)
        {
            // 销毁所有子对象
            foreach (Transform child in fastenersContainer)
            {
                Destroy(child.gameObject);
            }
        }

        // 隐藏场景中的螺丝
        if (screwsContainer != null)
        {
            foreach (Transform child in screwsContainer)
            {
                child.gameObject.SetActive(false);
            }
        }

        // 隐藏场景中的螺母
        if (nutsContainer != null)
        {
            foreach (Transform child in nutsContainer)
            {
                child.gameObject.SetActive(false);
            }
        }
    }

    /// <summary>
    /// 验证并更新参考点映射
    /// </summary>
    public void ValidateReferencePointMappings()
    {
        Debug.Log("验证参考点映射...");

        // 清空现有映射
        referencePointNameToIndex.Clear();

        // 重新创建映射
        foreach (var pair in partNameToComponent)
        {
            string partName = pair.Key;
            AssemblyPart part = pair.Value;

            // 验证参考点
            part.ValidateReferencePoints();

            // 重新映射参考点
            for (int i = 0; i < part.ReferencePointCount; i++)
            {
                Transform refPoint = part.GetReferencePoint(i);
                if (refPoint != null)
                {
                    // 创建多种可能的映射键
                    string refName = refPoint.name;
                    string fullRefName = $"{partName}_{refName}";

                    // 添加基本映射
                    referencePointNameToIndex[fullRefName] = i;

                    // 如果参考点名称是"P1"格式，也添加"{partName}-P1"格式的映射
                    if (refName.StartsWith("P") && int.TryParse(refName.Substring(1), out _))
                    {
                        string altRefName = $"{partName}-{refName}";
                        referencePointNameToIndex[altRefName] = i;
                    }

                    Debug.Log($"映射参考点: {fullRefName} -> 索引 {i}");
                }
            }
        }

        Debug.Log($"参考点映射验证完成，共 {referencePointNameToIndex.Count} 个映射");
    }



    /// <summary>
    /// 获取参考点索引
    /// </summary>
    /// <param name="partName">零件名称</param>
    /// <param name="refPointName">参考点名称</param>
    /// <returns>参考点索引，如果找不到则返回-1</returns>
    private int GetReferencePointIndex(string partName, string refPointName)
    {
        // 处理Neo4j中可能的格式差异
        string cleanRefName = refPointName;
        string refPartName = null;

        // 检查参考点名称是否包含"_P"（格式：零件名称_P1）
        int lastPIndex = refPointName.LastIndexOf("_P");
        if (lastPIndex > 0 && lastPIndex < refPointName.Length - 2)
        {
            // 提取零件名和参考点部分
            refPartName = refPointName.Substring(0, lastPIndex);
            cleanRefName = refPointName.Substring(lastPIndex + 1); // +1 to skip the underscore

            // 检查零件名是否匹配
            if (refPartName != partName)
            {
                Debug.LogWarning($"参考点 {refPointName} 的零件名 {refPartName} 与预期的零件名 {partName} 不匹配，但仍将继续处理");
            }

            Debug.Log($"从完整格式解析参考点: {refPointName} -> 零件名: {refPartName}, 参考点: {cleanRefName}");
        }
        // 如果参考点名称包含分隔符(如"-")，提取后半部分
        else if (refPointName.Contains("-"))
        {
            string[] parts = refPointName.Split('-');
            cleanRefName = parts[parts.Length - 1]; // 取最后一部分，通常是"P1"
            Debug.Log($"处理参考点名称: {refPointName} -> {cleanRefName}");
        }

        // 尝试直接从映射中获取
        string fullRefName = $"{partName}_{cleanRefName}";
        if (referencePointNameToIndex.TryGetValue(fullRefName, out int index))
        {
            Debug.Log($"通过映射找到参考点: {fullRefName} -> 索引 {index}");
            return index;
        }

        // 尝试直接使用原始名称从映射中获取
        if (referencePointNameToIndex.TryGetValue(refPointName, out index))
        {
            Debug.Log($"通过原始名称找到参考点: {refPointName} -> 索引 {index}");
            return index;
        }

        // 如果找不到，尝试解析参考点名称中的索引
        // 假设参考点名称格式为 "P0", "P1", "P2" 等
        if (cleanRefName.StartsWith("P") && int.TryParse(cleanRefName[1..], out int parsedIndex))
        {
            Debug.Log($"通过解析找到参考点: {cleanRefName} -> 索引 {parsedIndex}");
            return parsedIndex;
        }

        // 如果都失败，返回-1
        Debug.LogWarning($"无法解析参考点: {fullRefName}，原始名称: {refPointName}");
        return -1;
    }
}
