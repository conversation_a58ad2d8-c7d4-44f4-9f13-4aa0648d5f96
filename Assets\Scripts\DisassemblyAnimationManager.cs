using UnityEngine;
using System.Collections;
using System.Collections.Generic;

/// <summary>
/// 拆卸动画管理器 - 基于现有装配系统的逆向操作
/// </summary>
public class DisassemblyAnimationManager : MonoBehaviour
{
    [Header("拆卸动画配置")]
    [SerializeField] private float disassemblyDuration = 2.0f;
    [SerializeField] private float separationDistance = 0.1f; // 零件分离距离
    [SerializeField] private float fastenerRemovalHeight = 0.05f; // 紧固件移除高度
    [SerializeField] private float rotationAngle = 720f; // 拆卸旋转角度
    
    [Header("依赖组件")]
    [SerializeField] private AssemblyAnimationManager assemblyAnimationManager;
    
    private Dictionary<string, Vector3> originalPositions = new Dictionary<string, Vector3>();
    private Dictionary<string, Quaternion> originalRotations = new Dictionary<string, Quaternion>();

    void Start()
    {
        if (assemblyAnimationManager == null)
            assemblyAnimationManager = FindObjectOfType<AssemblyAnimationManager>();
    }

    /// <summary>
    /// 执行完整的拆卸动画序列
    /// </summary>
    public IEnumerator ExecuteDisassemblySequence(
        AssemblyPart movingPart,
        AssemblyPart targetPart,
        AssemblyPart screwPart = null,
        AssemblyPart nutPart = null,
        Transform holeRef = null,
        Vector3 holeDirection = Vector3.zero,
        int movingPartRefIndex = 0,
        int targetPartRefIndex = 0
    )
    {
        Debug.Log($"开始拆卸序列: {movingPart.PartName} 从 {targetPart.PartName}");

        // 保存原始位置用于后续恢复
        SaveOriginalTransforms(movingPart, targetPart, screwPart, nutPart);

        // 1. 拆卸螺母（如果存在）
        if (nutPart != null && holeRef != null)
        {
            Debug.Log("步骤1: 拆卸螺母");
            yield return RemoveNut(nutPart, holeRef, holeDirection);
        }

        // 2. 拆卸螺丝（如果存在）
        if (screwPart != null && holeRef != null)
        {
            Debug.Log("步骤2: 拆卸螺丝");
            yield return RemoveScrew(screwPart, holeRef, holeDirection);
        }

        // 3. 分离零件
        Debug.Log("步骤3: 分离零件");
        yield return SeparateParts(movingPart, targetPart, movingPartRefIndex, targetPartRefIndex);

        Debug.Log("拆卸序列完成");
    }

    /// <summary>
    /// 拆卸螺母动画 - 逆向螺母安装过程
    /// </summary>
    private IEnumerator RemoveNut(AssemblyPart nutPart, Transform holeRef, Vector3 holeDirection)
    {
        // 1. 逆向旋转螺母（松开）
        Quaternion currentRot = nutPart.PartTransform.rotation;
        Quaternion looseRot = currentRot * Quaternion.AngleAxis(-rotationAngle, holeDirection);
        yield return RotatePartInPlace(nutPart, looseRot, disassemblyDuration * 0.3f);

        // 2. 将螺母从螺丝末端移开
        Vector3 screwEndPosition = holeRef.position + holeDirection * 0.005f; // 假设螺丝长度
        yield return assemblyAnimationManager.MovePart(nutPart, screwEndPosition, null, disassemblyDuration * 0.2f);

        // 3. 移动螺母到移除位置（螺丝尾部上方和侧方）
        Vector3 nutRemovalPos = screwEndPosition + Vector3.up * fastenerRemovalHeight + 
                               holeDirection * separationDistance;
        yield return assemblyAnimationManager.MovePart(nutPart, nutRemovalPos, null, disassemblyDuration * 0.5f);
    }

    /// <summary>
    /// 拆卸螺丝动画 - 逆向螺丝安装过程
    /// </summary>
    private IEnumerator RemoveScrew(AssemblyPart screwPart, Transform holeRef, Vector3 holeDirection)
    {
        // 1. 逆向旋转螺丝（松开）
        Quaternion currentRot = screwPart.PartTransform.rotation;
        Quaternion looseRot = currentRot * Quaternion.AngleAxis(-rotationAngle, holeDirection);
        yield return RotatePartInPlace(screwPart, looseRot, disassemblyDuration * 0.4f);

        // 2. 从孔位中拔出螺丝
        Vector3 holePosition = holeRef.position;
        yield return assemblyAnimationManager.MovePart(screwPart, holePosition, null, disassemblyDuration * 0.3f);

        // 3. 移动螺丝到移除位置（采用弧形轨迹）
        Vector3 screwRemovalPos = holePosition - holeDirection * separationDistance + 
                                 Vector3.up * fastenerRemovalHeight;
        yield return MovePartWithArcTrajectory(screwPart, screwRemovalPos, disassemblyDuration * 0.3f);
    }

    /// <summary>
    /// 分离零件动画 - 创新的分离轨迹设计
    /// </summary>
    private IEnumerator SeparateParts(AssemblyPart movingPart, AssemblyPart targetPart, 
                                     int movingRefIndex, int targetRefIndex)
    {
        // 获取参考点
        Transform movingRef = movingPart.GetReferencePoint(movingRefIndex);
        Transform targetRef = targetPart.GetReferencePoint(targetRefIndex);

        // 计算分离方向 - 基于参考点的相对位置
        Vector3 separationDirection = (movingRef.position - targetRef.position).normalized;
        if (separationDirection.magnitude < 0.1f)
        {
            separationDirection = Vector3.up; // 默认向上分离
        }

        // 设计分离轨迹：先轻微抖动，然后螺旋上升分离
        yield return CreateSeparationShake(movingPart);
        yield return CreateSpiralSeparation(movingPart, separationDirection);
    }

    /// <summary>
    /// 创建分离前的轻微抖动效果
    /// </summary>
    private IEnumerator CreateSeparationShake(AssemblyPart part)
    {
        Vector3 originalPos = part.PartTransform.position;
        float shakeIntensity = 0.002f;
        int shakeCount = 6;

        for (int i = 0; i < shakeCount; i++)
        {
            Vector3 shakeOffset = new Vector3(
                Random.Range(-shakeIntensity, shakeIntensity),
                Random.Range(-shakeIntensity, shakeIntensity),
                Random.Range(-shakeIntensity, shakeIntensity)
            );
            
            yield return assemblyAnimationManager.MovePart(part, originalPos + shakeOffset, 
                                                         null, 0.1f);
        }

        // 回到原始位置
        yield return assemblyAnimationManager.MovePart(part, originalPos, null, 0.1f);
    }

    /// <summary>
    /// 创建螺旋上升分离轨迹
    /// </summary>
    private IEnumerator CreateSpiralSeparation(AssemblyPart part, Vector3 separationDirection)
    {
        Vector3 startPos = part.PartTransform.position;
        Vector3 endPos = startPos + separationDirection * separationDistance;
        
        float duration = disassemblyDuration * 0.6f;
        float elapsedTime = 0f;
        
        // 计算螺旋轨迹的垂直向量
        Vector3 perpendicular = Vector3.Cross(separationDirection, Vector3.up).normalized;
        if (perpendicular.magnitude < 0.1f)
        {
            perpendicular = Vector3.Cross(separationDirection, Vector3.forward).normalized;
        }

        while (elapsedTime < duration)
        {
            float t = elapsedTime / duration;
            
            // 基础直线插值
            Vector3 linearPos = Vector3.Lerp(startPos, endPos, t);
            
            // 添加螺旋效果
            float spiralRadius = 0.01f * (1 - t); // 螺旋半径逐渐减小
            float spiralAngle = t * 360f * 2; // 两圈螺旋
            Vector3 spiralOffset = perpendicular * Mathf.Cos(spiralAngle * Mathf.Deg2Rad) * spiralRadius +
                                  Vector3.Cross(separationDirection, perpendicular) * 
                                  Mathf.Sin(spiralAngle * Mathf.Deg2Rad) * spiralRadius;
            
            Vector3 finalPos = linearPos + spiralOffset;
            part.PartTransform.position = finalPos;
            
            elapsedTime += Time.deltaTime;
            yield return null;
        }
        
        part.PartTransform.position = endPos;
    }

    /// <summary>
    /// 弧形轨迹移动
    /// </summary>
    private IEnumerator MovePartWithArcTrajectory(AssemblyPart part, Vector3 targetPos, float duration)
    {
        Vector3 startPos = part.PartTransform.position;
        Vector3 midPoint = (startPos + targetPos) * 0.5f + Vector3.up * 0.02f; // 弧形中点
        
        float elapsedTime = 0f;
        
        while (elapsedTime < duration)
        {
            float t = elapsedTime / duration;
            
            // 贝塞尔曲线插值
            Vector3 currentPos = Mathf.Pow(1 - t, 2) * startPos + 
                               2 * (1 - t) * t * midPoint + 
                               Mathf.Pow(t, 2) * targetPos;
            
            part.PartTransform.position = currentPos;
            
            elapsedTime += Time.deltaTime;
            yield return null;
        }
        
        part.PartTransform.position = targetPos;
    }

    /// <summary>
    /// 原地旋转零件
    /// </summary>
    private IEnumerator RotatePartInPlace(AssemblyPart part, Quaternion targetRotation, float duration)
    {
        Quaternion startRotation = part.PartTransform.rotation;
        float elapsedTime = 0f;
        
        while (elapsedTime < duration)
        {
            float t = elapsedTime / duration;
            part.PartTransform.rotation = Quaternion.Slerp(startRotation, targetRotation, t);
            
            elapsedTime += Time.deltaTime;
            yield return null;
        }
        
        part.PartTransform.rotation = targetRotation;
    }

    /// <summary>
    /// 保存原始变换信息
    /// </summary>
    private void SaveOriginalTransforms(params AssemblyPart[] parts)
    {
        foreach (var part in parts)
        {
            if (part != null)
            {
                originalPositions[part.PartName] = part.PartTransform.position;
                originalRotations[part.PartName] = part.PartTransform.rotation;
            }
        }
    }

    /// <summary>
    /// 恢复原始位置（用于重置功能）
    /// </summary>
    public void RestoreOriginalTransforms()
    {
        foreach (var kvp in originalPositions)
        {
            GameObject part = GameObject.Find(kvp.Key);
            if (part != null)
            {
                part.transform.position = kvp.Value;
                part.transform.rotation = originalRotations[kvp.Key];
            }
        }
    }
}