using UnityEngine;
using UnityEditor;
using System.IO;

/// <summary>
/// FBX转Prefab工具类
/// 用于将FBX模型文件批量转换为Unity预制体
/// </summary>
public class FBXToPrefab : MonoBehaviour
{
    /// <summary>
    /// 在Unity菜单栏添加"Tools/Generate Prefabs From FBX"选项
    /// </summary>
    [MenuItem("Tools/Generate Prefabs From FBX")]
    static void GeneratePrefabs()
    {
        // 定义FBX资源路径和预制体保存路径
        string fbxPath = "robotic arms Middle"; // Resources目录下的路径
        string prefabFolder = "Assets/Prefabs/10scale";

        // 确保Prefabs目录存在，如果不存在则创建
        if (!AssetDatabase.IsValidFolder(prefabFolder))
        {
            AssetDatabase.CreateFolder("Assets", "Prefabs");
        }

        // 加载所有FBX模型
        GameObject[] fbxModels = Resources.LoadAll<GameObject>(fbxPath);
        if (fbxModels.Length == 0)
        {
            Debug.LogError("没有找到FBX资源，请确保FBX文件在`Resources/robotic arms Middle`目录下");
            return;
        }

        // 遍历每个FBX模型
        foreach (GameObject model in fbxModels)
        {
            // 创建模型实例
            GameObject instance = Instantiate(model);
            instance.name = model.name; // 保持名称一致
            
            // 将模型扩大十倍
           // instance.transform.localScale = new Vector3(10f, 10f, 10f);

            // 添加碰撞体组件
            MeshFilter meshFilter = instance.GetComponentInChildren<MeshFilter>();
            if (meshFilter != null)
            {
                // 如果模型有网格，添加网格碰撞体
                MeshCollider meshCollider = instance.AddComponent<MeshCollider>();
                meshCollider.sharedMesh = meshFilter.sharedMesh;
                meshCollider.convex = true; // 设置为凸面体，用于物理交互
            }
            else
            {
                // 如果没有网格，添加盒型碰撞体作为备选
                instance.AddComponent<BoxCollider>();
            }

            // 设置预制体保存路径
            string prefabPath = $"{prefabFolder}/{model.name}.prefab";

            // 保存为预制体
            PrefabUtility.SaveAsPrefabAsset(instance, prefabPath);
            DestroyImmediate(instance); // 删除场景中的临时实例

            Debug.Log($"已创建预制体: {prefabPath}");
        }

        // 刷新Unity资源数据库
        AssetDatabase.Refresh();
        Debug.Log("所有FBX资源已转换为预制体");
    }
}
