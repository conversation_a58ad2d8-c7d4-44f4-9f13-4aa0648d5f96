using UnityEngine;
using System.Collections;
using Unity.Mathematics;
using System;
using System.Collections.Generic;


public class AgentTools : MonoBehaviour
{
    // UI面板的预制件或单一面板（需在Inspector中赋值）
    public GameObject uiPanel;
    private Transform vrCamera; // VR相机，用于朝向计算

    void Start()
    {
        vrCamera = Camera.main.transform; // 假设主相机是VR相机
        if (uiPanel != null)
        {
            uiPanel.SetActive(false); // 初始隐藏
        }
    }

    // 1. 文本UI（使用激活/取消激活）
    public void ShowTextUI(string text)
    {
        if (uiPanel == null)
        {
            Debug.LogError("uiPanel is not assigned!");
            return;
        }

        TMPro.TextMeshProUGUI textComponent = uiPanel.GetComponentInChildren<TMPro.TextMeshProUGUI>();
        if (textComponent != null)
        {
            textComponent.text = text;
        }

        uiPanel.SetActive(true); // 激活面板
        uiPanel.transform.position = vrCamera.position + vrCamera.forward * 5f; // 更新位置

        //uiPanel.transform.LookAt(vrCamera); // 朝向用户
        uiPanel.transform.rotation = vrCamera.rotation;// 朝向用户

        StartCoroutine(HideUIPanelAfterDelay(5f)); // 5秒后自动隐藏
    }

    private IEnumerator HideUIPanelAfterDelay(float delay)
    {
        yield return new WaitForSeconds(delay);
        if (uiPanel != null && uiPanel.activeSelf)
        {
            uiPanel.SetActive(false); // 取消激活
        }
    }

    // 2. 高亮
    public void HighlightPart(string targetPartName)
    {
        GameObject part = GameObject.Find(targetPartName); // 替换为你的零件查找逻辑
        if (part != null)
        {
            Renderer renderer = part.GetComponent<Renderer>();
            List<GameObject> objects = DetectNearbyObjects(part,5f);
            foreach(GameObject obj in objects)
            {
                 PartTransparent(obj);
            }
            if (renderer != null)
            {
                renderer.material.color = Color.yellow; // 高亮颜色

                //RotateToUser(part); // 旋转模型朝向用户
                Debug.Log($"Highlighted {targetPartName} at {System.DateTime.Now}");
            }
        }
        else
        {
            Debug.LogWarning($"Part {targetPartName} not found");
        }
    }

    private void RotateToUser(GameObject obj)
    {
        Quaternion quaternion = Quaternion.Euler(-90, 0, 0);
        Vector3 targetDirection = vrCamera.position - obj.transform.position;
        obj.transform.rotation = Quaternion.LookRotation(targetDirection);
        obj.transform.rotation *= quaternion;

    }

    // 3. 两零件连线
    public void ConnectParts(string part1Name, string part2Name)
    {
        GameObject part1 = GameObject.Find(part1Name);
        GameObject part2 = GameObject.Find(part2Name);
        if (part1 != null && part2 != null)
        {
            StartCoroutine(DrawLineAnimation(part1.transform, part2.transform));
        }
        else
        {
            Debug.LogWarning("One or both parts not found");
        }
    }

    private IEnumerator DrawLineAnimation(Transform part1, Transform part2)
    {
        LineRenderer lineRenderer = new GameObject("TempLine").AddComponent<LineRenderer>();
        lineRenderer.startWidth = 1f;
        lineRenderer.endWidth = 1f;
        lineRenderer.material = new Material(Shader.Find("Sprites/Default"));

        for (int i = 0; i < 3; i++) // 重复三次
        {
            lineRenderer.SetPosition(0, part1.position);
            lineRenderer.SetPosition(1, part2.position);
            // RotateToUser(part1.gameObject); // 旋转模型
            yield return new WaitForSeconds(0.5f); // 显示0.5秒
            lineRenderer.SetPosition(0, Vector3.zero);
            lineRenderer.SetPosition(1, Vector3.zero);
            yield return new WaitForSeconds(0.5f); // 隐藏0.5秒
        }
        Destroy(lineRenderer.gameObject);
    }

    // 4. 建立层次关系认识
    public void HighlightSubgraphSequence(string[] partSequence)
    {
        StartCoroutine(HighlightSequence(partSequence));
    }

    private IEnumerator HighlightSequence(string[] parts)
    {
        foreach (string partName in parts)
        {
            GameObject part = GameObject.Find(partName);
            if (part != null)
            {
                Renderer renderer = part.GetComponent<Renderer>();
                if (renderer != null)
                {
                    renderer.material.color = Color.green;
                    yield return new WaitForSeconds(1f); // 每秒点亮一个
                    renderer.material.color = Color.white; // 恢复默认颜色
                }
            }
        }
    }

    // 5. 拆/装螺丝
    public void AssembleDisassembleScrew(Vector3 partPoint, string assemblyEdge, bool isAssemble)
    {
        Debug.Log($"{(isAssemble ? "Assembling" : "Disassembling")} screw at {partPoint} on edge {assemblyEdge} at {System.DateTime.Now}");
        // 假设已有动画函数，根据isAssemble调用不同动画
        // 示例：PlayAnimation(partPoint, assemblyEdge, isAssemble);
    }

    // 6. 引导用户检查零件
    public void GuideCheckParts(string[] partList)
    {
        StartCoroutine(DisplayPartCheck(partList));
    }

    private IEnumerator DisplayPartCheck(string[] parts)
    {
        GameObject displayArea = new GameObject("PartDisplayArea");
        displayArea.transform.position = vrCamera.position + vrCamera.forward * 1.0f;
        displayArea.transform.rotation = Quaternion.LookRotation(vrCamera.forward);

        foreach (string partName in parts)
        {
            GameObject partPrefab = Resources.Load<GameObject>(partName); // 假设零件为Prefab
            if (partPrefab != null)
            {
                GameObject instance = Instantiate(partPrefab, displayArea.transform);
                instance.transform.localPosition = new Vector3(UnityEngine.Random.Range(-0.5f, 0.5f), 0, UnityEngine.Random.Range(-0.5f, 0.5f));
                StartCoroutine(RotatePart(instance));
            }
        }

        yield return new WaitForSeconds(10f); // 显示10秒
        Destroy(displayArea);
    }

    private IEnumerator RotatePart(GameObject part)
    {
        while (true)
        {
            part.transform.Rotate(0, 30 * Time.deltaTime, 0); // 缓慢旋转
            yield return null;
        }
    }
    private void PartTransparent(GameObject part)
    {
        Renderer renderer = part.GetComponent<Renderer>();
        Material material = Resources.Load<Material>("Transparent");
        if (renderer != null)
        {
            renderer.material = material; // 半透明
        }
    }
      public static List<GameObject> DetectNearbyObjects(GameObject centerObject, float radius, LayerMask layerMask = default)
    {
        List<GameObject> nearbyObjects = new List<GameObject>();

        Vector3 center = centerObject.transform.position;
        Collider[] hitColliders;

        // 判断是否设置了 layerMask
        if (layerMask == default)
        {
            hitColliders = Physics.OverlapSphere(center, radius);
        }
        else
        {
            hitColliders = Physics.OverlapSphere(center, radius, layerMask);
        }

        foreach (Collider col in hitColliders)
        {
            // 排除自己
            if (col.gameObject != centerObject)
            {
                nearbyObjects.Add(col.gameObject);
            }
        }

        return nearbyObjects;
    }
}